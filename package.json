{"name": "btaskeezalo", "private": true, "version": "1.0.0", "description": "zmp-blank-templates", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"login": "zmp login", "start": "zmp start", "deploy": "zmp deploy"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@tanstack/react-query": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jotai": "^2.12.1", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zmp-sdk": "latest", "zmp-ui": "latest", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/css": "^0.10.0", "@eslint/js": "^9.31.0", "@eslint/json": "^0.13.1", "@eslint/markdown": "^7.1.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "postcss": "^8.4.38", "postcss-cli": "^8.3.1", "postcss-preset-env": "^6.7.0", "prettier": "3.6.2", "sass": "^1.76.0", "tailwindcss": "^3.4.3", "typescript-eslint": "^8.38.0", "vite": "^5.2.13", "zmp-vite-plugin": "latest"}}