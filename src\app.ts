// src\app.ts
import 'zmp-ui/zaui.css'
import '@/styles/tailwind.scss'
import '@/styles/app.scss'

import React from 'react'
import { createRoot } from 'react-dom/client'

import { App } from '@/app/App'
import { AppConfig } from '@/shared/types'

import appConfig from '../app-config.json'

if (!window.APP_CONFIG) {
  window.APP_CONFIG = appConfig as AppConfig
}

const root = createRoot(document.getElementById('app')!)
root.render(React.createElement(App))
